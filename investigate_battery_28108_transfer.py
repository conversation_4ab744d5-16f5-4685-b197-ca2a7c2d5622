#!/usr/bin/env python3
"""
Investigate the missing transfer event for battery 28108 between vehicles.
"""

import pandas as pd
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def investigate_battery_28108_transfer():
    """Investigate the missing transfer for battery 28108."""

    print("=" * 80)
    print("INVESTIGATING BATTERY 28108 TRANSFER INCONSISTENCY")
    print("=" * 80)

    # Load repair data
    df = pd.read_csv("hv_repair_2025-06-02b.csv")

    # Convert dates
    df["created"] = pd.to_datetime(df["created"], errors="coerce")
    df["battery_changed"] = pd.to_datetime(df["battery_changed"], errors="coerce")
    df["effective_date"] = df["battery_changed"].fillna(df["created"])

    print("🔍 DETAILED ANALYSIS:")
    print("=" * 50)

    # 1. Find all events for battery 28108
    battery_28108_events = df[
        (df["battery_id_old"] == "28108") | (df["battery_id_new"] == "28108")
    ]

    print(f"📋 ALL EVENTS FOR BATTERY 28108:")
    print(
        "Date       | VIN                | Created    | Battery Changed | Action  | Old    → New"
    )
    print("-" * 95)

    for _, row in battery_28108_events.sort_values("effective_date").iterrows():
        created_str = (
            row["created"].strftime("%Y-%m-%d") if pd.notna(row["created"]) else "None"
        )
        changed_str = (
            row["battery_changed"].strftime("%Y-%m-%d")
            if pd.notna(row["battery_changed"])
            else "None"
        )
        effective_str = (
            row["effective_date"].strftime("%Y-%m-%d")
            if pd.notna(row["effective_date"])
            else "None"
        )

        print(
            f"{effective_str} | {row['vin']} | {created_str} | {changed_str:15} | {row['action']:7} | {row['battery_id_old'] or 'None':6} → {row['battery_id_new'] or 'None'}"
        )

    # 2. Check both vehicles involved
    vin1 = "WS5D16JAAKA100043"  # Where 28108 was installed
    vin2 = "WS5D16JAAJA104454"  # Where 28108 was removed

    print(f"\n🚗 VEHICLE ANALYSIS:")
    print("=" * 50)

    print(f"\nVehicle 1: {vin1} (where 28108 was INSTALLED)")
    vin1_events = df[df["vin"] == vin1].sort_values("effective_date")

    if len(vin1_events) > 0:
        print("Date       | Action  | Old Battery → New Battery")
        print("-" * 50)
        for _, row in vin1_events.iterrows():
            effective_str = (
                row["effective_date"].strftime("%Y-%m-%d")
                if pd.notna(row["effective_date"])
                else "None"
            )
            print(
                f"{effective_str} | {row['action']:7} | {row['battery_id_old'] or 'None':11} → {row['battery_id_new'] or 'None'}"
            )
    else:
        print("No repair events found for this vehicle")

    print(f"\nVehicle 2: {vin2} (where 28108 was REMOVED)")
    vin2_events = df[df["vin"] == vin2].sort_values("effective_date")

    if len(vin2_events) > 0:
        print("Date       | Action  | Old Battery → New Battery")
        print("-" * 50)
        for _, row in vin2_events.iterrows():
            effective_str = (
                row["effective_date"].strftime("%Y-%m-%d")
                if pd.notna(row["effective_date"])
                else "None"
            )
            print(
                f"{effective_str} | {row['action']:7} | {row['battery_id_old'] or 'None':11} → {row['battery_id_new'] or 'None'}"
            )
    else:
        print("No repair events found for this vehicle")

    # 3. Look for ANY events between 2023-08-30 and 2023-09-04 that might explain the transfer
    print(f"\n🔍 EVENTS BETWEEN 2023-08-30 AND 2023-09-04:")
    print("=" * 50)

    start_date = datetime(2023, 8, 30)
    end_date = datetime(2023, 9, 4)

    # Check for any events involving either vehicle or battery 28108
    between_events = df[
        (df["effective_date"] >= start_date)
        & (df["effective_date"] <= end_date)
        & (
            (df["vin"].isin([vin1, vin2]))
            | (df["battery_id_old"] == "28108")
            | (df["battery_id_new"] == "28108")
        )
    ].sort_values("effective_date")

    if len(between_events) > 0:
        print("Date       | VIN                | Action  | Old    → New    | Comment")
        print("-" * 75)
        for _, row in between_events.iterrows():
            effective_str = (
                row["effective_date"].strftime("%Y-%m-%d")
                if pd.notna(row["effective_date"])
                else "None"
            )
            comment = ""
            if row["battery_id_old"] == "28108" or row["battery_id_new"] == "28108":
                comment = "🔋 Involves 28108"
            if row["vin"] in [vin1, vin2]:
                comment += " 🚗 Target vehicle"

            print(
                f"{effective_str} | {row['vin']} | {row['action']:7} | {row['battery_id_old'] or 'None':6} → {row['battery_id_new'] or 'None':7} | {comment}"
            )
    else:
        print(
            "❌ NO events found between these dates involving the vehicles or battery"
        )

    # 4. Search for installation events of 28108 in vehicle 2
    print(f"\n🔍 SEARCH FOR MISSING INSTALLATION IN {vin2}:")
    print("=" * 50)

    vin2_battery_installs = df[(df["vin"] == vin2) & (df["battery_id_new"] == "28108")]

    if len(vin2_battery_installs) > 0:
        print("Found installation events:")
        for _, row in vin2_battery_installs.iterrows():
            effective_str = (
                row["effective_date"].strftime("%Y-%m-%d")
                if pd.notna(row["effective_date"])
                else "None"
            )
            print(
                f"  {effective_str} | {row['action']} | {row['battery_id_old'] or 'None'} → {row['battery_id_new']}"
            )
    else:
        print(f"❌ NO installation events found for battery 28108 in vehicle {vin2}")
        print("This confirms the data inconsistency!")

    # 5. Check if there are any removal events for 28108 from vehicle 1
    print(f"\n🔍 SEARCH FOR MISSING REMOVAL FROM {vin1}:")
    print("=" * 50)

    vin1_battery_removals = df[(df["vin"] == vin1) & (df["battery_id_old"] == "28108")]

    if len(vin1_battery_removals) > 0:
        print("Found removal events:")
        for _, row in vin1_battery_removals.iterrows():
            effective_str = (
                row["effective_date"].strftime("%Y-%m-%d")
                if pd.notna(row["effective_date"])
                else "None"
            )
            print(
                f"  {effective_str} | {row['action']} | {row['battery_id_old']} → {row['battery_id_new'] or 'None'}"
            )
    else:
        print(f"❌ NO removal events found for battery 28108 from vehicle {vin1}")
        print("This confirms the data inconsistency!")

    # 6. Summary and implications
    print(f"\n📊 SUMMARY OF FINDINGS:")
    print("=" * 50)

    print("✅ CONFIRMED EVENTS:")
    print(f"  1. 2023-08-30: Battery 28108 INSTALLED in {vin1}")
    print(f"  2. 2023-09-04: Battery 28108 REMOVED from {vin2}")
    print()

    print("❌ MISSING EVENTS:")
    print(f"  1. No record of battery 28108 being REMOVED from {vin1}")
    print(f"  2. No record of battery 28108 being INSTALLED in {vin2}")
    print()

    print("🔍 IMPLICATIONS FOR OUR ALGORITHM:")
    print("  • Our algorithm correctly identifies the logical inconsistency")
    print("  • It creates a single period from installation to removal")
    print("  • But assigns it to the REMOVAL vehicle (the last known location)")
    print("  • Duration calculation is correct (5 days)")
    print("  • However, the vehicle assignment might be misleading")
    print()

    print("⚠️ DATA QUALITY ISSUES:")
    print("  • Incomplete repair records")
    print("  • Missing transfer documentation")
    print("  • Possible manual/undocumented battery moves")
    print("  • Could indicate data entry errors")
    print()

    print("🔧 POTENTIAL IMPROVEMENTS:")
    print("  • Flag cross-vehicle periods as 'uncertain_location'")
    print("  • Split periods at vehicle boundaries")
    print("  • Add confidence penalty for cross-vehicle events")
    print("  • Report potential data quality issues")


if __name__ == "__main__":
    investigate_battery_28108_transfer()
