#!/usr/bin/env python3
"""
Focused analysis of battery 28108 to test the negative duration bug fix.
"""

from data_preparation_pipeline import BatteryTimelinePipeline
from datetime import datetime
import logging

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def analyze_battery_28108():
    """Analyze battery 28108 specifically to test the negative duration fix."""

    print("=" * 80)
    print("BATTERY 28108 ANALYSIS - TESTING NEGATIVE DURATION FIX")
    print("=" * 80)

    # Initialize pipeline with test batteries including 28108
    test_batteries = ["28108", "27493", "27349"]  # Include related batteries

    pipeline = BatteryTimelinePipeline(
        hv_repair_file="hv_repair_2025-06-02b.csv",
        working_matching_vehicles_file="comparison_results/working_matching_vehicles.csv",
        working_unique_vehicles_file="comparison_results/working_unique_vehicles.csv",
        test_batteries=test_batteries,
    )

    print(f"🔍 Analyzing battery 28108 and related batteries: {test_batteries}")
    print()

    # Run Phase 1
    print("PHASE 1: Loading and cleaning data...")
    phase1_stats = pipeline.phase1_load_and_clean_data()

    # Examine raw events for battery 28108
    print(f"\n📋 RAW EVENTS FOR BATTERY 28108:")
    battery_28108_events = [
        e
        for e in pipeline.cleaned_events
        if e.get("battery_id_old") == "28108" or e.get("battery_id_new") == "28108"
    ]

    print("Date       | VIN                | Event Type | Old    | New    | Action")
    print("-" * 75)
    for event in sorted(battery_28108_events, key=lambda x: x["date"]):
        print(
            f"{event['date'].strftime('%Y-%m-%d')} | {event['vin']} | {event['event_type']:10} | {event['battery_id_old'] or 'None':6} | {event['battery_id_new'] or 'None':6} | {event['action']}"
        )

    # Run Phase 2
    print(f"\nPHASE 2: Building battery timeline...")
    phase2_stats = pipeline.phase2_unified_timeline_building()

    # Find timeline periods for battery 28108
    battery_28108_periods = [
        p for p in pipeline.battery_timelines if p["battery_id"] == "28108"
    ]

    print(f"\n📊 TIMELINE RESULTS FOR BATTERY 28108:")
    print(f"Found {len(battery_28108_periods)} periods")
    print()

    if battery_28108_periods:
        print(
            "Battery | VIN                | Start Date | End Date   | Duration | Confidence | Stage     | Note"
        )
        print("-" * 110)

        for period in battery_28108_periods:
            start_str = (
                period["start_date"].strftime("%Y-%m-%d")
                if period["start_date"]
                else "None"
            )
            end_str = (
                period["end_date"].strftime("%Y-%m-%d")
                if period["end_date"]
                else "Active"
            )
            duration = (
                period["duration_days"]
                if period["duration_days"] is not None
                else "N/A"
            )

            # Highlight negative duration in red
            duration_display = (
                f"⚠️ {duration}"
                if isinstance(duration, int) and duration < 0
                else str(duration)
            )

            print(
                f"{period['battery_id']:7} | {period['vin']} | {start_str:10} | {end_str:10} | {duration_display:8} | {period['confidence']:10} | {period['lifecycle_stage']:9} | {period['note'][:50]}..."
            )
    else:
        print("❌ No timeline periods found for battery 28108")

    # Analyze the expected vs actual timeline
    print(f"\n🔍 DETAILED ANALYSIS:")
    print("=" * 50)

    print("Expected timeline based on raw events:")
    print("1. 2023-07-21 (created) / 2023-08-30 (battery_changed): 27493 → 28108")
    print("   → Battery 28108 INSTALLED on 2023-08-30")
    print("2. 2023-07-24 (created) / 2023-09-04 (battery_changed): 28108 → 27349")
    print("   → Battery 28108 REMOVED on 2023-09-04")
    print("Expected result: 28108 active from 2023-08-30 to 2023-09-04 (5 days)")
    print()

    if battery_28108_periods:
        period = battery_28108_periods[0]
        actual_start = period["start_date"]
        actual_end = period["end_date"]
        actual_duration = period["duration_days"]

        print("Actual result from our algorithm:")
        print(f"Start: {actual_start.strftime('%Y-%m-%d') if actual_start else 'None'}")
        print(f"End: {actual_end.strftime('%Y-%m-%d') if actual_end else 'None'}")
        print(f"Duration: {actual_duration} days")
        print()

        # Check if the fix worked
        if actual_duration is not None and actual_duration >= 0:
            print("✅ SUCCESS: Negative duration bug is FIXED!")
            print(f"Duration is now positive: {actual_duration} days")
        elif actual_duration is not None and actual_duration < 0:
            print("❌ FAILURE: Negative duration bug still exists!")
            print(f"Duration is still negative: {actual_duration} days")
            print("The fix may not have been applied correctly.")
        else:
            print("⚠️ WARNING: Duration is None - need to investigate further")

        # Check date validation notes
        if "Revalidated" in period.get("note", ""):
            print(f"\n🔧 Date validation was applied:")
            print(f"Note: {period['note']}")
        else:
            print(f"\n📝 No date validation adjustments:")
            print(f"Note: {period['note']}")

    # Test activity data availability
    print(f"\n🔍 ACTIVITY DATA ANALYSIS:")
    print("=" * 50)

    for period in battery_28108_periods:
        vin = period["vin"]
        if vin in pipeline.vin_to_vehicle_id:
            vehicle_id = pipeline.vin_to_vehicle_id[vin]
            print(f"VIN {vin} → Vehicle ID: {vehicle_id}")

            # Check activity around the period dates
            if period["start_date"] and period["end_date"]:
                start_activity = pipeline._query_vehicle_activity_on_date(
                    vin, period["start_date"]
                )
                end_activity = pipeline._query_vehicle_activity_on_date(
                    vin, period["end_date"]
                )

                print(
                    f"Activity on start date ({period['start_date'].strftime('%Y-%m-%d')}):"
                )
                if start_activity:
                    print(f"  Has activity: {start_activity['has_km_activity']}")
                    print(f"  Details: {start_activity.get('details', 'N/A')}")
                else:
                    print(f"  No activity data found")

                print(
                    f"Activity on end date ({period['end_date'].strftime('%Y-%m-%d')}):"
                )
                if end_activity:
                    print(f"  Has activity: {end_activity['has_km_activity']}")
                    print(f"  Details: {end_activity.get('details', 'N/A')}")
                else:
                    print(f"  No activity data found")
        else:
            print(f"VIN {vin} not found in activity mapping")

    # Show related batteries for context
    print(f"\n🔗 RELATED BATTERIES ANALYSIS:")
    print("=" * 50)

    for battery_id in ["27493", "27349"]:
        related_periods = [
            p for p in pipeline.battery_timelines if p["battery_id"] == battery_id
        ]

        if related_periods:
            print(f"\nBattery {battery_id}:")
            for period in related_periods:
                start_str = (
                    period["start_date"].strftime("%Y-%m-%d")
                    if period["start_date"]
                    else "None"
                )
                end_str = (
                    period["end_date"].strftime("%Y-%m-%d")
                    if period["end_date"]
                    else "Active"
                )
                duration = (
                    period["duration_days"]
                    if period["duration_days"] is not None
                    else "N/A"
                )
                print(
                    f"  {period['vin']} | {start_str} to {end_str} | {duration} days | {period['confidence']} confidence"
                )

    print(f"\n{'='*80}")
    print("ANALYSIS COMPLETE")
    print(f"{'='*80}")


if __name__ == "__main__":
    analyze_battery_28108()
