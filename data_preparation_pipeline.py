#!/usr/bin/env python3
"""
Enhanced Battery Timeline Pipeline - Streamlined 3-Phase Approach

This script creates comprehensive battery timelines using a streamlined 3-phase approach:

Phase 1: Data Loading and Cleaning
- Load repair events, snapshots, vehicle info, and vehicle activity data
- Clean and validate data with comprehensive validation rules
- Build caches for efficient processing

Phase 2: Unified Timeline Building (Battery-Centric)
- Group events by VIN, sort by descending date (prioritize recent data)
- For each unique battery: filter events, apply backward/forward chaining
- Handle edge cases inline (orphaned removals, implied installations, transfers)
- Use vehicle activity data for gap inference and validation
- Single-pass processing with immediate confidence scoring

Phase 3: Comprehensive Validation and Quality Assurance
- Validate timelines against vehicle activity patterns
- Perform final deduplication and conflict resolution
- Assign lifecycle stages with activity-based validation
- Generate quality metrics and export results

Key Features:
- Battery-centric processing with descending date sorting
- PostgreSQL integration for vehicle activity validation
- Comprehensive validation rules for km and SOC data
- Inline edge case handling reduces processing loops
- Vehicle activity integration for better gap inference
- Enhanced validation using fleet patterns (avg 20km/day, 12% SOC usage)

Output format:
battery_id | vin | start_date | end_date | confidence | source | lifecycle_stage | km_validated | activity_validated
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional, Set
import warnings
import psycopg2
from sqlalchemy import create_engine, text
import os

warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class BatteryTimelinePipeline:
    """
    Battery Timeline Pipeline with Streamlined 3-Phase Approach

    Phase 1: Data Loading and Cleaning
    Phase 2: Unified Timeline Building (Battery-Centric)
    Phase 3: Comprehensive Validation and Quality Assurance
    """

    def __init__(
        self,
        hv_repair_file: str,
        working_matching_vehicles_file: str,
        working_unique_vehicles_file: str,
        db_connection_string: str = None,
        test_batteries: List[str] = None,
    ):
        """Initialize with file paths and optional database connection."""
        self.hv_repair_file = hv_repair_file
        self.working_matching_vehicles_file = working_matching_vehicles_file
        self.working_unique_vehicles_file = working_unique_vehicles_file
        self.test_batteries = (
            test_batteries  # Filter for specific batteries if provided
        )

        # Database connection
        self.db_connection_string = (
            db_connection_string or self._get_default_db_connection()
        )
        self.db_engine = None

        # Fleet patterns for validation
        self.avg_km_per_day = 20
        self.avg_soc_usage_per_day = 12  # percentage points

        # Phase 1 data containers
        self.hv_repair_df = None
        self.working_vehicles_df = None
        self.cleaned_events = []
        self.vehicle_info_cache = {}
        self.vehicle_activity_cache = {}
        self.vin_to_vehicle_id = {}
        self.global_battery_cache = {}

        # Phase 2 data containers
        self.battery_timelines = []
        self.timeline_stats = {}

        # Phase 3 data containers
        self.final_timeline = []
        self.quality_stats = {}

        # Processing statistics
        self.phase_stats = {}

    def _get_default_db_connection(self) -> str:
        """Get default database connection string from environment or config."""
        host = os.getenv("DB_HOST", "localhost")
        port = os.getenv("DB_PORT", "6543")
        database = os.getenv("DB_NAME", "LeitwartenDB")
        user = os.getenv("DB_USER", "datadump")
        password = os.getenv("DB_PASSWORD", "pAUjuLftyHURa5Ra")

        return f"postgresql://{user}:{password}@{host}:{port}/{database}"

    # =================================================================
    # PHASE 1: DATA LOADING AND CLEANING
    # =================================================================

    def phase1_load_and_clean_data(self) -> Dict:
        """Phase 1: Load and clean all data sources with comprehensive validation."""
        logger.info("=== PHASE 1: DATA LOADING AND CLEANING ===")

        # Initialize database connection
        self._initialize_database_connection()

        # Load core data
        self._load_hv_repair_data()
        self._load_working_vehicles_data()
        self._load_vehicle_activity_data()

        # Clean and validate
        self._clean_repair_events()
        self._build_vehicle_info_cache()
        self._build_global_battery_cache()
        self._validate_data_quality()

        phase1_stats = {
            "raw_repair_records": len(self.hv_repair_df),
            "raw_vehicle_records": len(self.working_vehicles_df),
            "clean_events": len(self.cleaned_events),
            "unique_vins": len(self.vehicle_info_cache),
            "vehicles_with_activity": len(self.vin_to_vehicle_id),
            "global_batteries": len(self.global_battery_cache),
            "data_quality_score": self._calculate_data_quality_score(),
        }

        logger.info(f"Phase 1 complete: {phase1_stats}")
        return phase1_stats

    def _initialize_database_connection(self):
        """Initialize PostgreSQL database connection."""
        try:
            self.db_engine = create_engine(self.db_connection_string)
            # Test connection
            with self.db_engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                logger.info("✅ Database connection established successfully")
        except Exception as e:
            logger.warning(f"❌ Database connection failed: {e}")
            logger.warning("Proceeding without activity validation")
            self.db_engine = None

    def _load_hv_repair_data(self) -> pd.DataFrame:
        """Load and perform basic cleaning of HV repair data."""
        logger.info(f"Loading HV repair data from: {self.hv_repair_file}")

        self.hv_repair_df = pd.read_csv(self.hv_repair_file, sep=",")
        logger.info(f"Loaded {len(self.hv_repair_df):,} HV repair records")

        # Clean dates
        self.hv_repair_df["created"] = pd.to_datetime(
            self.hv_repair_df["created"], errors="coerce"
        )
        self.hv_repair_df["battery_changed"] = self.hv_repair_df[
            "battery_changed"
        ].replace("--", None)
        self.hv_repair_df["battery_changed"] = pd.to_datetime(
            self.hv_repair_df["battery_changed"], errors="coerce"
        )

        # Create effective date
        self.hv_repair_df["effective_date"] = self.hv_repair_df[
            "battery_changed"
        ].fillna(self.hv_repair_df["created"])

        # Clean battery IDs
        for col in ["battery_id_old", "battery_id_new"]:
            self.hv_repair_df[col] = self.hv_repair_df[col].astype(str)
            self.hv_repair_df[col] = self.hv_repair_df[col].replace(
                ["nan", "", " ", "None"], None
            )

        # Filter valid records
        initial_count = len(self.hv_repair_df)
        self.hv_repair_df = self.hv_repair_df.dropna(
            subset=["vin", "effective_date", "action"]
        )
        self.hv_repair_df = self.hv_repair_df[self.hv_repair_df["vin"].str.len() > 10]

        # Filter for test batteries if specified
        if self.test_batteries:
            logger.info(f"Filtering for test batteries: {self.test_batteries}")
            test_battery_mask = self.hv_repair_df["battery_id_old"].isin(
                self.test_batteries
            ) | self.hv_repair_df["battery_id_new"].isin(self.test_batteries)
            self.hv_repair_df = self.hv_repair_df[test_battery_mask]
            logger.info(
                f"After test battery filtering: {len(self.hv_repair_df):,} records"
            )

        filtered_count = initial_count - len(self.hv_repair_df)
        if filtered_count > 0:
            logger.warning(f"Filtered out {filtered_count:,} invalid HV repair records")

        self.hv_repair_df = self.hv_repair_df.sort_values(["effective_date", "vin"])
        logger.info(f"Clean HV repair data: {len(self.hv_repair_df):,} records")

        return self.hv_repair_df

    def _load_working_vehicles_data(self) -> pd.DataFrame:
        """Load and combine working vehicles data from both files."""
        logger.info(f"Loading working vehicles data...")
        logger.info(f"  Matching vehicles file: {self.working_matching_vehicles_file}")
        logger.info(f"  Unique vehicles file: {self.working_unique_vehicles_file}")

        # Load both files
        matching_df = pd.read_csv(self.working_matching_vehicles_file)
        unique_df = pd.read_csv(self.working_unique_vehicles_file)

        logger.info(f"Loaded {len(matching_df):,} matching vehicle records")
        logger.info(f"Loaded {len(unique_df):,} unique vehicle records")

        # Combine both dataframes
        self.working_vehicles_df = pd.concat(
            [matching_df, unique_df], ignore_index=True
        )
        logger.info(
            f"Combined working vehicles data: {len(self.working_vehicles_df):,} records"
        )

        # Clean erstzulassung date (vehicle rollout date)
        self.working_vehicles_df["erstzulassung"] = pd.to_datetime(
            self.working_vehicles_df["erstzulassung"], errors="coerce"
        )

        # Clean battery IDs
        for col in ["master", "slave"]:
            if col in self.working_vehicles_df.columns:
                self.working_vehicles_df[col] = self.working_vehicles_df[col].astype(
                    str
                )
                self.working_vehicles_df[col] = self.working_vehicles_df[col].replace(
                    ["nan", "", " ", "None"], None
                )

        # Clean VIN and AKZ
        for col in ["vin", "akz"]:
            if col in self.working_vehicles_df.columns:
                self.working_vehicles_df[col] = (
                    self.working_vehicles_df[col].astype(str).str.strip()
                )
                self.working_vehicles_df[col] = self.working_vehicles_df[col].replace(
                    ["nan", "", " ", "None"], None
                )

        return self.working_vehicles_df

    def _load_vehicle_activity_data(self):
        """Load VIN to vehicle_id mapping and pre-cache activity for erstzulassung dates."""
        if not self.db_engine:
            logger.warning("No database connection - skipping activity data loading")
            return

        try:
            logger.info("Loading VIN to vehicle_id mapping...")

            # Load only the VIN to vehicle_id mapping (lightweight)
            mapping_query = """
            SELECT vin, vehicle_id
            FROM public.vehicles 
            WHERE vin IS NOT NULL
            """

            mapping_df = pd.read_sql(mapping_query, self.db_engine)
            logger.info(f"Loaded VIN mapping for {len(mapping_df):,} vehicles")

            # Build VIN to vehicle_id mapping
            self.vin_to_vehicle_id = {}
            for _, row in mapping_df.iterrows():
                vin = row["vin"]
                if pd.notna(vin):
                    self.vin_to_vehicle_id[vin] = row["vehicle_id"]

            logger.info(
                f"Built VIN to vehicle_id mapping for {len(self.vin_to_vehicle_id)} vehicles"
            )

            # Pre-cache activity data for erstzulassung dates for performance
            self._precache_erstzulassung_activity()

        except Exception as e:
            logger.error(f"Failed to load VIN to vehicle_id mapping: {e}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            # Don't fail completely, just continue without activity data
            self.vin_to_vehicle_id = {}

    def _precache_erstzulassung_activity(self):
        """Pre-cache activity validation for all unique VINs at their erstzulassung dates for performance."""
        logger.info("Pre-caching activity data for erstzulassung dates...")

        # Collect unique VINs with erstzulassung dates
        erstzulassung_dates = {}
        for vin, vehicle_info in self.vehicle_info_cache.items():
            erstzulassung = vehicle_info.get("erstzulassung")
            if erstzulassung and vin in self.vin_to_vehicle_id:
                erstzulassung_dates[vin] = erstzulassung

        logger.info(
            f"Pre-caching activity data for {len(erstzulassung_dates)} VINs with erstzulassung dates..."
        )

        # Pre-load activity data for these dates
        cached_count = 0
        for vin, erstzulassung in erstzulassung_dates.items():
            activity_data = self._query_vehicle_activity_on_date(vin, erstzulassung)
            if activity_data is not None:
                # Store in cache with date key for quick lookup
                cache_key = f"{vin}_{erstzulassung.date()}"
                if not hasattr(self, "erstzulassung_activity_cache"):
                    self.erstzulassung_activity_cache = {}
                self.erstzulassung_activity_cache[cache_key] = activity_data
                cached_count += 1

        logger.info(f"Pre-cached activity data for {cached_count} erstzulassung dates")

    def _query_vehicle_activity_on_date(
        self, vin: str, target_date: datetime
    ) -> Optional[Dict]:
        """Query vehicle activity data for a specific date, handling multiple records."""
        if not self.db_engine or vin not in self.vin_to_vehicle_id:
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            # Updated query to handle multiple records per date
            activity_query = text(
                """
                SELECT date, km_start, km_end
                FROM public.daily_stats 
                WHERE vehicle_id = :vehicle_id 
                AND date = :target_date
                ORDER BY timestamp_start ASC
                """
            )

            with self.db_engine.connect() as conn:
                result = conn.execute(
                    activity_query,
                    {"vehicle_id": vehicle_id, "target_date": target_date.date()},
                )
                rows = result.fetchall()  # Get ALL records, not just first one

                if not rows:
                    return None

                # Check if ANY record shows valid activity (≥2km movement)
                has_valid_activity = False
                valid_records = []
                all_km_starts = []
                all_km_ends = []

                for row in rows:
                    date, km_start, km_end = row[0], row[1], row[2]

                    # Collect all km values for summary
                    if km_start is not None:
                        all_km_starts.append(km_start)
                    if km_end is not None:
                        all_km_ends.append(km_end)

                    # Check if this individual record shows valid activity
                    if (
                        km_start is not None
                        and km_end is not None
                        and km_end > km_start
                        and km_end - km_start >= 2
                    ):
                        has_valid_activity = True
                        valid_records.append(
                            {
                                "km_start": km_start,
                                "km_end": km_end,
                                "km_diff": km_end - km_start,
                            }
                        )

                # Use the first record's date, but activity validation from all records
                first_row = rows[0]

                return {
                    "date": first_row[0],
                    "km_start": min(all_km_starts) if all_km_starts else None,
                    "km_end": max(all_km_ends) if all_km_ends else None,
                    "has_km_activity": has_valid_activity,
                    "total_records": len(rows),
                    "valid_records": len(valid_records),
                    "details": f"Found {len(rows)} records, {len(valid_records)} with ≥2km activity",
                }

        except Exception as e:
            logger.debug(
                f"Error querying activity data for VIN {vin} on {target_date}: {e}"
            )
            return None

    def _query_vehicle_activity_on_fly(
        self, vin: str, query_type: str = "summary"
    ) -> Optional[Dict]:
        """Query vehicle activity data on-the-fly without caching."""
        if not self.db_engine or vin not in self.vin_to_vehicle_id:
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            if query_type == "summary":
                # Quick summary query
                activity_query = text(
                    """
                SELECT 
                    COUNT(date) as total_days,
                    MIN(date) as first_activity_date,
                    MAX(date) as last_activity_date,
                    AVG(CASE 
                        WHEN km_end >= km_start 
                        AND km_end >= 0 
                        AND km_start >= 0 
                        THEN km_end - km_start 
                        ELSE NULL 
                    END) as avg_daily_km
                FROM public.daily_stats 
                WHERE vehicle_id = :vehicle_id
                """
                )
            elif query_type == "first_active":
                # Find first active date only
                activity_query = text(
                    """
                SELECT date, km_start, km_end
                FROM public.daily_stats 
                WHERE vehicle_id = :vehicle_id 
                AND km_end - km_start >= 2 
                AND km_end > km_start 
                AND km_start >= 0
                ORDER BY date ASC
                LIMIT 1
                """
                )
            else:
                return None

            with self.db_engine.connect() as conn:
                result = conn.execute(activity_query, {"vehicle_id": vehicle_id})
                row = result.fetchone()

                if row:
                    if query_type == "summary":
                        return {
                            "vehicle_id": vehicle_id,
                            "total_days": row[0],
                            "first_activity_date": row[1],
                            "last_activity_date": row[2],
                            "avg_daily_km": row[3],
                        }
                    elif query_type == "first_active":
                        return {
                            "first_active_date": datetime.combine(
                                row[0], datetime.min.time()
                            ),
                            "km_start": row[1],
                            "km_end": row[2],
                        }

                return None

        except Exception as e:
            logger.debug(f"Error querying activity data for VIN {vin}: {e}")
            return None

    def _calculate_vehicle_data_quality_from_values(
        self, total_days: int, days_with_km: int, invalid_km_records: int
    ) -> float:
        """Calculate data quality score from individual values (0-1)."""
        if total_days == 0:
            return 0.0

        # Completeness score based on KM data
        km_completeness = days_with_km / total_days if total_days > 0 else 0

        # Validity score based on KM data
        km_validity = 1 - (invalid_km_records / total_days) if total_days > 0 else 0

        # Combined score (100% based on KM data)
        return round((km_completeness * 0.6 + km_validity * 0.4), 3)

    def _calculate_vehicle_data_quality(self, row) -> float:
        """Calculate data quality score for a vehicle based on KM data only (0-1)."""
        if row["total_days"] == 0:
            return 0.0

        # Completeness score based on KM data
        km_completeness = (
            row["days_with_km"] / row["total_days"] if row["total_days"] > 0 else 0
        )

        # Validity score based on KM data
        km_validity = (
            1 - (row["invalid_km_records"] / row["total_days"])
            if row["total_days"] > 0
            else 0
        )

        # Combined score (100% based on KM data)
        return round((km_completeness * 0.6 + km_validity * 0.4), 3)

    def _clean_repair_events(self):
        """Clean and classify repair events with enhanced validation."""
        logger.info("Cleaning and classifying repair events...")

        self.cleaned_events = []

        for _, row in self.hv_repair_df.iterrows():
            # Basic event structure
            event = {
                "date": row["effective_date"],
                "vin": row["vin"],
                "battery_id_old": (
                    row["battery_id_old"] if pd.notna(row["battery_id_old"]) else None
                ),
                "battery_id_new": (
                    row["battery_id_new"] if pd.notna(row["battery_id_new"]) else None
                ),
                "action": row["action"],
                "raw_data": row.to_dict(),
            }

            # Classify event type based on battery IDs
            if event["battery_id_old"] and event["battery_id_new"]:
                event["event_type"] = "change"
                event["clarity"] = "clear"
            elif event["battery_id_new"] and not event["battery_id_old"]:
                event["event_type"] = "install"
                event["clarity"] = "clear"
            elif event["battery_id_old"] and not event["battery_id_new"]:
                event["event_type"] = "remove_or_confirm"
                event["clarity"] = "ambiguous"
            else:
                event["event_type"] = "unclear"
                event["clarity"] = "invalid"

            # Enhanced validation flags
            event["flags"] = []
            if not event["battery_id_old"] and not event["battery_id_new"]:
                event["flags"].append("no_battery_ids")
            if event["date"] < datetime(2020, 1, 1):
                event["flags"].append("old_date")
            if event["date"] > datetime.now():
                event["flags"].append("future_date")

            # Activity validation will be done on-the-fly when needed
            # Re-enable activity validation for comprehensive analysis

            self.cleaned_events.append(event)

        logger.info(f"Cleaned {len(self.cleaned_events)} repair events")

        # Count by clarity
        clarity_counts = {}
        for event in self.cleaned_events:
            clarity = event["clarity"]
            clarity_counts[clarity] = clarity_counts.get(clarity, 0) + 1

        logger.info(f"Event clarity distribution: {clarity_counts}")

    def _build_vehicle_info_cache(self):
        """Build enhanced vehicle information cache."""
        logger.info("Building vehicle information cache...")

        self.vehicle_info_cache = {}

        if self.working_vehicles_df is not None:
            for _, row in self.working_vehicles_df.iterrows():
                vin = row.get("vin")
                if vin and pd.notna(vin):
                    self.vehicle_info_cache[vin] = {
                        "vin": vin,
                        "erstzulassung": row.get("erstzulassung"),
                        "master_battery": row.get("master"),
                        "slave_battery": row.get("slave"),
                        "akz": row.get("akz"),
                    }

        # Add VINs from repair data that aren't in vehicle data
        repair_vins = set(event["vin"] for event in self.cleaned_events)
        vehicle_vins = set(self.vehicle_info_cache.keys())
        missing_vins = repair_vins - vehicle_vins

        for vin in missing_vins:
            self.vehicle_info_cache[vin] = {
                "vin": vin,
                "erstzulassung": None,
                "master_battery": None,
                "slave_battery": None,
                "akz": None,
                "source": "repair_only",
            }

        # Merge with activity data (will be loaded on-demand)
        # Activity data will be loaded when needed via _load_vehicle_activity_on_demand

        logger.info(f"Built cache for {len(self.vehicle_info_cache)} vehicles")

    def _build_global_battery_cache(self):
        """Build global battery cache for cross-VIN transfer tracking."""
        logger.info("Building global battery cache...")

        self.global_battery_cache = {}

        # Process all battery IDs from repair events
        for event in self.cleaned_events:
            for battery_field in ["battery_id_old", "battery_id_new"]:
                battery_id = event.get(battery_field)
                if battery_id:
                    if battery_id not in self.global_battery_cache:
                        self.global_battery_cache[battery_id] = {
                            "battery_id": battery_id,
                            "vins_used": set(),
                            "first_seen": event["date"],
                            "last_seen": event["date"],
                            "install_events": [],
                            "remove_events": [],
                        }

                    cache_entry = self.global_battery_cache[battery_id]
                    cache_entry["vins_used"].add(event["vin"])
                    cache_entry["first_seen"] = min(
                        cache_entry["first_seen"], event["date"]
                    )
                    cache_entry["last_seen"] = max(
                        cache_entry["last_seen"], event["date"]
                    )

                    if battery_field == "battery_id_new":
                        cache_entry["install_events"].append(event)
                    else:
                        cache_entry["remove_events"].append(event)

        # Add snapshot batteries
        for vin, vehicle_info in self.vehicle_info_cache.items():
            for battery_field in ["master_battery", "slave_battery"]:
                battery_id = vehicle_info.get(battery_field)
                if battery_id and pd.notna(battery_id):
                    if battery_id not in self.global_battery_cache:
                        self.global_battery_cache[battery_id] = {
                            "battery_id": battery_id,
                            "vins_used": {vin},
                            "first_seen": vehicle_info.get("erstzulassung")
                            or datetime.min,
                            "last_seen": datetime.now(),
                            "install_events": [],
                            "remove_events": [],
                            "snapshot_only": True,
                        }
                    else:
                        self.global_battery_cache[battery_id]["vins_used"].add(vin)

        logger.info(
            f"Built global cache for {len(self.global_battery_cache)} batteries"
        )

    def _validate_data_quality(self):
        """Enhanced data quality validation."""
        logger.info("Validating data quality...")

        # Activity-based validation
        activity_issues = 0
        for event in self.cleaned_events:
            vin = event["vin"]
            if vin in self.vehicle_activity_cache:
                activity = self.vehicle_activity_cache[vin]

                # Check for repairs during inactive periods
                if self._is_vehicle_inactive_on_date(vin, event["date"]):
                    event["flags"].append("repair_during_inactive_period")
                    activity_issues += 1

        if activity_issues > 0:
            logger.warning(f"Found {activity_issues} repairs during inactive periods")

    def _is_vehicle_inactive_on_date(self, vin: str, date: datetime) -> bool:
        """Check if vehicle was inactive on a specific date based on daily stats."""
        if not self.db_engine or vin not in self.vin_to_vehicle_id:
            return False

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            # Query specific date activity
            query = text(
                """
                SELECT km_start, km_end, soc_start_percent, soc_end_percent
                FROM daily_stats 
                WHERE vehicle_id = :vehicle_id 
                AND date = :date
            """
            )

            with self.db_engine.connect() as conn:
                result = conn.execute(
                    query, {"vehicle_id": vehicle_id, "date": date.date()}
                )
                row = result.fetchone()

                if not row:
                    return True  # No data = inactive

                # Check if km and SOC indicate no activity
                km_start, km_end = row[0], row[1]
                soc_start, soc_end = row[2], row[3]

                # No movement and no SOC usage suggests inactivity
                no_km_change = (km_start == km_end) if km_start and km_end else False
                no_soc_change = (
                    (soc_start == soc_end) if soc_start and soc_end else False
                )

                return no_km_change and no_soc_change

        except Exception as e:
            logger.debug(f"Error checking activity for {vin} on {date}: {e}")
            return False

    def _calculate_data_quality_score(self) -> float:
        """Calculate overall data quality score (0-1)."""
        if not self.cleaned_events:
            return 0.0

        total_events = len(self.cleaned_events)
        clear_events = len([e for e in self.cleaned_events if e["clarity"] == "clear"])
        flagged_events = len([e for e in self.cleaned_events if e["flags"]])

        # Score based on clarity and flags
        clarity_score = clear_events / total_events
        flag_penalty = flagged_events / total_events

        quality_score = max(0.0, clarity_score - (flag_penalty * 0.5))
        return round(quality_score, 3)

    # =================================================================
    # PHASE 2: UNIFIED TIMELINE BUILDING (BATTERY-CENTRIC)
    # =================================================================

    def phase2_unified_timeline_building(self) -> Dict:
        """Phase 2: Pure battery-centric timeline building with descending date sorting."""
        logger.info("=== PHASE 2: PURE BATTERY-CENTRIC TIMELINE BUILDING ===")

        # Sort ALL events by descending date (latest events first)
        sorted_events = sorted(
            self.cleaned_events, key=lambda x: x["date"], reverse=True
        )

        logger.info(
            f"Processing {len(sorted_events)} events in descending date order (latest first)"
        )

        # Extract all unique battery IDs from all events
        all_batteries = set()
        for event in sorted_events:
            if event.get("battery_id_old"):
                all_batteries.add(event["battery_id_old"])
            if event.get("battery_id_new"):
                all_batteries.add(event["battery_id_new"])

        logger.info(f"Found {len(all_batteries)} unique batteries to process")

        # Process each battery independently
        self.battery_timelines = []
        timeline_stats = {
            "batteries_processed": 0,
            "periods_created": 0,
            "edge_cases_handled": 0,
            "cross_vin_transfers": 0,
        }

        for battery_id in all_batteries:
            battery_periods = self._build_battery_timeline_pure(
                battery_id, sorted_events
            )
            self.battery_timelines.extend(battery_periods)

            timeline_stats["batteries_processed"] += 1
            timeline_stats["periods_created"] += len(battery_periods)

            # Count cross-VIN transfers
            vins_in_periods = set(period["vin"] for period in battery_periods)
            if len(vins_in_periods) > 1:
                timeline_stats["cross_vin_transfers"] += 1

        # Add snapshot-only batteries that weren't found in repair events
        processed_batteries = set(
            period["battery_id"] for period in self.battery_timelines
        )
        snapshot_periods = self._add_snapshot_only_batteries(processed_batteries)
        self.battery_timelines.extend(snapshot_periods)
        timeline_stats["periods_created"] += len(snapshot_periods)

        self.timeline_stats = timeline_stats
        logger.info(f"Phase 2 complete: {timeline_stats}")
        return timeline_stats

    def _build_battery_timeline_pure(
        self, battery_id: str, all_events: List[Dict]
    ) -> List[Dict]:
        """Build timeline for a specific battery using pure battery-centric approach with descending event processing."""

        # Filter events for this battery (already sorted by descending date)
        battery_events = [
            event
            for event in all_events
            if event.get("battery_id_old") == battery_id
            or event.get("battery_id_new") == battery_id
        ]

        if not battery_events:
            return []

        periods = []
        processed_pairs = (
            set()
        )  # Track processed installation-removal pairs to avoid duplicates

        # Process events from latest to earliest (already sorted descending)
        # Track the "current state" moving backwards in time
        is_currently_active = False
        current_active_vin = None

        # First pass: determine if battery is currently active (look at latest events)
        for event in battery_events:
            if event.get("battery_id_new") == battery_id:
                # Battery was installed - it might be currently active
                is_currently_active = True
                current_active_vin = event["vin"]
                break
            elif event.get("battery_id_old") == battery_id and event.get(
                "battery_id_new"
            ):
                # Battery was replaced - not currently active
                is_currently_active = False
                break

        # Process events to build periods
        for i, event in enumerate(battery_events):
            event_vin = event["vin"]
            event_date = event["date"]

            # Determine what this event means for the battery
            if event.get("battery_id_new") == battery_id:
                # Battery is being installed in this event

                # Create unique pair identifier
                pair_id = f"install_{event_vin}_{event_date.isoformat()}"
                if pair_id in processed_pairs:
                    continue

                if i == 0 and is_currently_active:
                    # This is the latest installation and battery is currently active
                    start_date, method, confidence = (
                        self._find_battery_start_with_confidence(
                            battery_id, None, event_vin
                        )
                    )
                    if start_date:
                        # Apply date validation with adjustment for start date
                        validated_start, start_adjustment = self._find_validated_date(
                            event_vin, event_date, "forward"
                        )

                        # Use validated date if found, otherwise use original
                        final_start = validated_start if validated_start else event_date

                        # Add adjustment note
                        final_note = f"Currently active: {method}"
                        if start_adjustment > 0:
                            final_note += (
                                f" | Revalidated: start_adjusted_+{start_adjustment}d"
                            )

                        periods.append(
                            self._create_period(
                                battery_id,
                                event_vin,
                                final_start,
                                None,
                                "repair_event",
                                confidence,
                                "active",
                                note=final_note,
                            )
                        )
                        processed_pairs.add(pair_id)
                else:
                    # This installation will end at the next removal
                    next_removal = None
                    for j in range(
                        i
                    ):  # Look at earlier processed events (more recent dates)
                        if (
                            battery_events[j].get("battery_id_old") == battery_id
                            and battery_events[j]["vin"] == event_vin
                        ):
                            next_removal = battery_events[j]
                            break

                    if next_removal:
                        # Create unique pair identifier for this install-remove sequence
                        removal_pair_id = (
                            f"remove_{event_vin}_{next_removal['date'].isoformat()}"
                        )
                        if removal_pair_id not in processed_pairs:
                            start_date, method, confidence = (
                                self._find_battery_start_with_confidence(
                                    battery_id, event_date, event_vin
                                )
                            )
                            if start_date:
                                # Apply date validation with adjustment if needed
                                validated_start, start_adjustment = (
                                    self._find_validated_date(
                                        event_vin, event_date, "forward"
                                    )
                                )
                                validated_end, end_adjustment = (
                                    self._find_validated_date(
                                        event_vin, next_removal["date"], "backward"
                                    )
                                )

                                # Use validated dates if found, otherwise use original
                                final_start = (
                                    validated_start if validated_start else event_date
                                )
                                final_end = (
                                    validated_end
                                    if validated_end
                                    else next_removal["date"]
                                )

                                # CRITICAL FIX: Ensure start_date <= end_date after adjustments
                                if (
                                    final_start
                                    and final_end
                                    and final_start > final_end
                                ):
                                    logger.warning(
                                        f"Date adjustment created invalid range for battery {battery_id}: "
                                        f"start {final_start.date()} > end {final_end.date()}. "
                                        f"Reverting to original dates."
                                    )
                                    # Revert to original dates to prevent negative duration
                                    final_start = event_date
                                    final_end = next_removal["date"]
                                    start_adjustment = 0
                                    end_adjustment = 0

                                # Add adjustment notes
                                adjustment_notes = []
                                if start_adjustment > 0:
                                    adjustment_notes.append(
                                        f"start_adjusted_+{start_adjustment}d"
                                    )
                                if end_adjustment > 0:
                                    adjustment_notes.append(
                                        f"end_adjusted_-{end_adjustment}d"
                                    )

                                final_note = f"Historical period: {method}"
                                if adjustment_notes:
                                    final_note += (
                                        f" | Revalidated: {', '.join(adjustment_notes)}"
                                    )

                                periods.append(
                                    self._create_period(
                                        battery_id,
                                        event_vin,
                                        final_start,
                                        final_end,
                                        "repair_event",
                                        confidence,
                                        "replaced",
                                        note=final_note,
                                    )
                                )
                                processed_pairs.add(pair_id)
                                processed_pairs.add(removal_pair_id)

            elif event.get("battery_id_old") == battery_id:
                # Battery is being removed/replaced in this event

                # Create unique pair identifier
                pair_id = f"remove_{event_vin}_{event_date.isoformat()}"
                if pair_id in processed_pairs:
                    continue

                if event.get("battery_id_new"):
                    # Clear replacement (battery_id_old -> battery_id_new)
                    # Find the installation that started this period
                    period_start = None
                    start_method = None
                    start_confidence = "low"
                    installation_found = False

                    for j in range(
                        i + 1, len(battery_events)
                    ):  # Look at later events (earlier dates)
                        if (
                            battery_events[j].get("battery_id_new") == battery_id
                            and battery_events[j]["vin"] == event_vin
                        ):
                            period_start = battery_events[j]["date"]
                            start_method = "installation_event"
                            start_confidence = "high"
                            installation_found = True

                            # Check if this installation was already processed
                            install_pair_id = (
                                f"install_{event_vin}_{period_start.isoformat()}"
                            )
                            if install_pair_id in processed_pairs:
                                installation_found = False  # Skip this pair
                            else:
                                processed_pairs.add(install_pair_id)
                            break

                    # If no installation found, try to infer start
                    if not period_start and not installation_found:
                        # Try to find sequential battery connection first
                        sequential_start = self._find_sequential_battery_start(
                            battery_id, event_date, event_vin, all_events
                        )
                        if sequential_start:
                            period_start, start_method, start_confidence = (
                                sequential_start
                            )
                            installation_found = True
                        else:
                            # Fallback to original method
                            start_date, method, confidence = (
                                self._find_battery_start_with_confidence(
                                    battery_id, event_date, event_vin
                                )
                            )
                            if start_date:
                                period_start = start_date
                                start_method = method
                                start_confidence = confidence
                                installation_found = True

                    if period_start and installation_found:
                        # Apply date validation with adjustment if needed
                        validated_start, start_adjustment = self._find_validated_date(
                            event_vin, period_start, "forward"
                        )
                        validated_end, end_adjustment = self._find_validated_date(
                            event_vin, event_date, "backward"
                        )

                        # Use validated dates if found, otherwise use original
                        final_start = (
                            validated_start if validated_start else period_start
                        )
                        final_end = validated_end if validated_end else event_date

                        # CRITICAL FIX: Ensure start_date <= end_date after adjustments
                        if final_start and final_end and final_start > final_end:
                            logger.warning(
                                f"Date adjustment created invalid range for battery {battery_id}: "
                                f"start {final_start.date()} > end {final_end.date()}. "
                                f"Reverting to original dates."
                            )
                            # Revert to original dates to prevent negative duration
                            final_start = period_start
                            final_end = event_date
                            start_adjustment = 0
                            end_adjustment = 0

                        # Add adjustment notes
                        adjustment_notes = []
                        if start_adjustment > 0:
                            adjustment_notes.append(
                                f"start_adjusted_+{start_adjustment}d"
                            )
                        if end_adjustment > 0:
                            adjustment_notes.append(f"end_adjusted_-{end_adjustment}d")

                        final_note = f"Replacement resolved: {start_method}"
                        if adjustment_notes:
                            final_note += (
                                f" | Revalidated: {', '.join(adjustment_notes)}"
                            )

                        # CROSS-VEHICLE DETECTION: Check if installation and removal are in different vehicles
                        installation_vin = None
                        if start_method == "installation_event":
                            # Find the actual installation event VIN
                            for install_event in [
                                e
                                for e in self.cleaned_events
                                if e.get("battery_id_new") == battery_id
                                and e["date"] == period_start
                            ]:
                                installation_vin = install_event["vin"]
                                break

                        if installation_vin and installation_vin != event_vin:
                            # Cross-vehicle period detected
                            final_note += f" | ⚠️ CROSS-VEHICLE: Installed in {installation_vin}, removed from {event_vin}"
                            start_confidence = (
                                "low"  # Reduce confidence due to data inconsistency
                            )
                            logger.warning(
                                f"Cross-vehicle inconsistency detected for battery {battery_id}: "
                                f"installed in {installation_vin} but removed from {event_vin}"
                            )

                        periods.append(
                            self._create_period(
                                battery_id,
                                event_vin,
                                final_start,
                                final_end,
                                "repair_event",
                                start_confidence,
                                "replaced",
                                note=final_note,
                            )
                        )
                        processed_pairs.add(pair_id)

                else:
                    # Ambiguous removal (battery_id_old only)
                    # Try sequential method first, then fallback to activity-based
                    sequential_start = self._find_sequential_battery_start(
                        battery_id, event_date, event_vin, all_events
                    )
                    if sequential_start:
                        start_date, method, confidence = sequential_start
                    else:
                        start_date, method, confidence = (
                            self._find_battery_start_with_confidence(
                                battery_id, event_date, event_vin
                            )
                        )

                    # Use effective_date (already handles battery_changed → created fallback)
                    end_date = event_date  # event_date is already the effective_date

                    if start_date:
                        # Apply date validation with adjustment if needed
                        validated_start, start_adjustment = self._find_validated_date(
                            event_vin, start_date, "forward"
                        )
                        validated_end, end_adjustment = self._find_validated_date(
                            event_vin, end_date, "backward"
                        )

                        # Use validated dates if found, otherwise use original
                        final_start = validated_start if validated_start else start_date
                        final_end = validated_end if validated_end else end_date

                        # CRITICAL FIX: Ensure start_date <= end_date after adjustments
                        if final_start and final_end and final_start > final_end:
                            logger.warning(
                                f"Date adjustment created invalid range for battery {battery_id}: "
                                f"start {final_start.date()} > end {final_end.date()}. "
                                f"Reverting to original dates."
                            )
                            # Revert to original dates to prevent negative duration
                            final_start = start_date
                            final_end = end_date
                            start_adjustment = 0
                            end_adjustment = 0

                        # Add adjustment notes
                        adjustment_notes = []
                        if start_adjustment > 0:
                            adjustment_notes.append(
                                f"start_adjusted_+{start_adjustment}d"
                            )
                        if end_adjustment > 0:
                            adjustment_notes.append(f"end_adjusted_-{end_adjustment}d")

                        final_note = f"Ambiguous removal resolved: {method}"
                        if adjustment_notes:
                            final_note += (
                                f" | Revalidated: {', '.join(adjustment_notes)}"
                            )

                        periods.append(
                            self._create_period(
                                battery_id,
                                event_vin,
                                final_start,
                                final_end,
                                "repair_event",
                                confidence,
                                "removed",
                                note=final_note,
                            )
                        )
                        processed_pairs.add(pair_id)

        # Re-enable activity validation for comprehensive analysis
        periods = self._validate_periods_with_activity(periods)

        return periods

    def _handle_ambiguous_removal(self, battery_id: str, event: Dict) -> Optional[Dict]:
        """Handle ambiguous removal events inline (battery_id_old only)."""
        vin = event["vin"]
        date = event["date"]

        # Use enhanced logic to find start date
        start_date, method, confidence = self._find_battery_start_with_confidence(
            battery_id, date, vin
        )

        if start_date:
            return self._create_period(
                battery_id,
                vin,
                start_date,
                date,
                "repair_event",
                confidence,
                "removed",
                note=f"Ambiguous removal resolved: {method}",
            )
        else:
            # Create period with unknown start
            return self._create_period(
                battery_id,
                vin,
                None,
                date,
                "repair_event",
                "low",
                "removed",
                note="Ambiguous removal - start unknown",
            )

    def _create_period(
        self,
        battery_id: str,
        vin: str,
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        source: str,
        confidence: str,
        lifecycle_stage: str,
        note: str = "",
    ) -> Dict:
        """Create a standardized battery period."""
        duration_days = None
        if start_date and end_date:
            duration_days = (end_date - start_date).days

        return {
            "battery_id": battery_id,
            "vin": vin,
            "start_date": start_date,
            "end_date": end_date,
            "duration_days": duration_days,
            "source": source,
            "confidence": confidence,
            "lifecycle_stage": lifecycle_stage,
            "phase_created": "phase2",
            "note": note,
            "km_validated": False,
            "activity_validated": False,
        }

    def _apply_inline_backward_chaining(
        self, battery_id: str, periods: List[Dict]
    ) -> List[Dict]:
        """Apply backward chaining to fill missing start dates."""
        for period in periods:
            if period["start_date"] is None and period["end_date"]:
                start_date, method, confidence = (
                    self._find_battery_start_with_confidence(
                        battery_id, period["end_date"], period["vin"]
                    )
                )

                if start_date:
                    period["start_date"] = start_date
                    period["duration_days"] = (period["end_date"] - start_date).days
                    period["confidence"] = confidence
                    period["note"] = f"Backward chaining: {method}"

        return periods

    def _find_sequential_battery_start(
        self, battery_id: str, removal_date: datetime, vin: str, all_events: List[Dict]
    ) -> Optional[Tuple[datetime, str, str]]:
        """Find battery start date by looking for the previous battery removal in the same VIN."""

        # Find all battery removal/replacement events for this VIN before the current removal
        vin_events = [
            event
            for event in all_events
            if event["vin"] == vin and event["date"] < removal_date
        ]

        if not vin_events:
            return None

        # Sort by date (descending - most recent first)
        vin_events.sort(key=lambda x: x["date"], reverse=True)

        # Look for the most recent previous battery removal/replacement
        for event in vin_events:
            # Check if this event represents a battery being removed/replaced
            if event.get("battery_id_old") and event.get("battery_id_new"):
                # Clear replacement: previous battery removed, current battery could have started here
                previous_removal_date = event["date"]
                logger.debug(
                    f"Found sequential connection: Battery {battery_id} likely started after {event['battery_id_old']} was removed on {previous_removal_date}"
                )
                return (
                    previous_removal_date,
                    f"sequential_after_{event['battery_id_old']}",
                    "high",
                )
            elif event.get("battery_id_old") and not event.get("battery_id_new"):
                # Ambiguous removal: previous battery removed, current battery could have started here
                previous_removal_date = event["date"]
                previous_battery = event["battery_id_old"]
                logger.debug(
                    f"Found sequential connection: Battery {battery_id} likely started after {previous_battery} ambiguous removal on {previous_removal_date}"
                )

                # Use effective_date which already handles battery_changed → created fallback
                return (
                    previous_removal_date,  # This is already the effective_date from event["date"]
                    f"sequential_after_{previous_battery}",
                    "high",
                )

        return None

    def _find_battery_start_with_confidence(
        self, battery_id: str, before_date: Optional[datetime], vin: str
    ) -> Tuple[Optional[datetime], str, str]:
        """Enhanced battery start finding with activity-based validation."""

        # Priority 1: Direct installation events
        install_events = [
            e
            for e in self.cleaned_events
            if e["battery_id_new"] == battery_id
            and (not before_date or e["date"] < before_date)
        ]
        if install_events:
            earliest = min(install_events, key=lambda x: x["date"])
            # Validate installation with activity data
            if self._validate_battery_installation_with_activity(vin, earliest["date"]):
                return earliest["date"], "installation_event", "high"
            else:
                return earliest["date"], "installation_event", "medium"

        # Priority 2: Implied installations (battery_id in old OR new, but not both)
        implied_events = [
            e
            for e in self.cleaned_events
            if (
                # Case 1: battery_id in old, new is empty (removal/confirmation)
                (e["battery_id_old"] == battery_id and not e["battery_id_new"])
                # Case 2: battery_id in new, old is empty (installation without removal)
                or (e["battery_id_new"] == battery_id and not e["battery_id_old"])
            )
            and (not before_date or e["date"] < before_date)
        ]
        if implied_events:
            earliest = min(implied_events, key=lambda x: x["date"])
            # Validate implied installation with activity data
            if self._validate_battery_installation_with_activity(vin, earliest["date"]):
                return earliest["date"], "implied_installation", "high"
            else:
                return earliest["date"], "implied_installation", "medium"

        # Priority 3: Cross-VIN installations
        cross_vin_events = [
            e
            for e in self.cleaned_events
            if e["battery_id_new"] == battery_id
            and e["vin"] != vin
            and (not before_date or e["date"] < before_date)
        ]
        if cross_vin_events:
            earliest = min(cross_vin_events, key=lambda x: x["date"])
            # Validate cross-VIN installation with activity data
            if self._validate_battery_installation_with_activity(vin, earliest["date"]):
                return (
                    earliest["date"],
                    f"cross_vin_from_{earliest['vin']}",
                    "high",
                )
            else:
                return (
                    earliest["date"],
                    f"cross_vin_from_{earliest['vin']}",
                    "medium",
                )

        # Priority 4: Activity-based start date (for vehicles with good activity data)
        activity_start = self._find_activity_based_start_date(vin, before_date)
        if activity_start:
            start_date, method, confidence = activity_start
            return start_date, method, confidence

        # Priority 5: Cross-VIN snapshot matching with activity validation
        if battery_id in self.global_battery_cache:
            vins_used = self.global_battery_cache[battery_id]["vins_used"]

            for other_vin in vins_used:
                if other_vin == vin:
                    continue

                other_vehicle_info = self.vehicle_info_cache.get(other_vin, {})
                other_snapshot_batteries = [
                    other_vehicle_info.get("master_battery"),
                    other_vehicle_info.get("slave_battery"),
                ]

                if battery_id in other_snapshot_batteries:
                    # Check if there are any installation events for this battery in the other VIN
                    other_vin_installations = [
                        e
                        for e in self.cleaned_events
                        if e["battery_id_new"] == battery_id
                        and e["vin"] == other_vin
                        and (not before_date or e["date"] < before_date)
                    ]

                    if other_vin_installations:
                        # Use the most recent installation event instead of snapshot date
                        latest_installation = max(
                            other_vin_installations, key=lambda x: x["date"]
                        )
                        return (
                            latest_installation["date"],
                            f"cross_vin_installation_from_{other_vin}",
                            "high",
                        )
                    else:
                        # Try activity-based start for cross-VIN scenario first
                        activity_start = self._find_activity_based_start_date(
                            other_vin, before_date
                        )
                        if activity_start:
                            start_date, method, confidence = activity_start
                            return (
                                start_date,
                                f"cross_vin_snapshot_from_{other_vin}_{method}",
                                confidence,
                            )
                        else:
                            # Fallback to erstzulassung for cross-VIN if no activity data
                            erstzulassung = other_vehicle_info.get("erstzulassung")
                            if erstzulassung and (
                                not before_date or erstzulassung < before_date
                            ):
                                return (
                                    erstzulassung,
                                    f"cross_vin_snapshot_from_{other_vin}_erstzulassung",
                                    "medium",
                                )

        # Priority 6: Snapshot matching with activity validation (fallback for current VIN)
        vehicle_info = self.vehicle_info_cache.get(vin, {})
        snapshot_batteries = [
            vehicle_info.get("master_battery"),
            vehicle_info.get("slave_battery"),
        ]

        if battery_id in snapshot_batteries:
            # First check if there are installation events for this battery in the current VIN
            current_vin_installations = [
                e
                for e in self.cleaned_events
                if e["battery_id_new"] == battery_id
                and e["vin"] == vin
                and (not before_date or e["date"] < before_date)
            ]

            if current_vin_installations:
                # Use the most recent installation event - this is actual event data (high priority)
                latest_installation = max(
                    current_vin_installations, key=lambda x: x["date"]
                )
                if self._validate_battery_installation_with_activity(
                    vin, latest_installation["date"]
                ):
                    return (
                        latest_installation["date"],
                        "current_vin_installation",
                        "high",
                    )
                else:
                    return (
                        latest_installation["date"],
                        "current_vin_installation",
                        "medium",
                    )

            # Fallback to erstzulassung only if no installation events exist
            erstzulassung = vehicle_info.get("erstzulassung")
            if erstzulassung and (not before_date or erstzulassung < before_date):
                # Try activity-based start first
                activity_start = self._find_activity_based_start_date(vin, before_date)
                if activity_start:
                    start_date, method, confidence = activity_start
                    return start_date, f"snapshot_activity_{method}", confidence
                else:
                    # Check if VIN has poor activity data quality
                    if vin in self.vehicle_activity_cache:
                        activity = self.vehicle_activity_cache[vin]
                        if activity["data_quality_score"] < 0.5:  # Poor activity data
                            return (
                                erstzulassung,
                                "snapshot_erstzulassung_poor_activity",
                                "medium",
                            )

                    # Fallback to erstzulassung if no activity data
                    return erstzulassung, "snapshot_erstzulassung_no_activity", "low"

        # Priority 7: Global battery cache fallback
        if battery_id in self.global_battery_cache:
            cache_entry = self.global_battery_cache[battery_id]
            earliest_date = cache_entry["first_seen"]
            if not before_date or earliest_date < before_date:
                return earliest_date, "global_cache_earliest", "low"

        return None, "no_start_found", "low"

    def _find_activity_based_start_date(
        self, vin: str, before_date: Optional[datetime]
    ) -> Optional[Tuple[datetime, str, str]]:
        """Find the earliest date when vehicle was actually active based on KM activity data only."""
        if not self.db_engine or vin not in self.vin_to_vehicle_id:
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            # Query to find the earliest active date with meaningful KM activity only
            query = text(
                """
                SELECT date, km_start, km_end
                FROM public.daily_stats 
                WHERE vehicle_id = :vehicle_id 
                AND date <= :before_date
                AND km_end - km_start >= 2 
                AND km_end > km_start 
                AND km_start >= 0
                ORDER BY date ASC
                LIMIT 1
            """
            )

            before_date_query = (
                before_date.date() if before_date else datetime.now().date()
            )

            with self.db_engine.connect() as conn:
                result = conn.execute(
                    query, {"vehicle_id": vehicle_id, "before_date": before_date_query}
                )
                row = result.fetchone()

                if row:
                    earliest_active_date = datetime.combine(row[0], datetime.min.time())
                    km_start, km_end = row[1], row[2]
                    km_diff = km_end - km_start

                    # Determine confidence based on KM activity level
                    if km_diff >= 10:  # Significant activity
                        method = "earliest_active_km_high"
                        confidence = "high"
                    elif km_diff >= 5:  # Moderate activity
                        method = "earliest_active_km_medium"
                        confidence = "high"
                    else:  # Minimal but valid activity (>=2km)
                        method = "earliest_active_km_minimal"
                        confidence = "medium"

                    return earliest_active_date, method, confidence

        except Exception as e:
            logger.debug(f"Error finding activity-based start date for {vin}: {e}")

        return None

    def _find_validated_date(
        self,
        vin: str,
        target_date: datetime,
        direction: str = "forward",
        max_days: int = 14,
    ) -> Tuple[Optional[datetime], int]:
        """
        Find a date with valid activity by adjusting target_date iteratively.

        Args:
            vin: Vehicle VIN
            target_date: Initial date to validate
            direction: "forward" for start dates (increase), "backward" for end dates (decrease)
            max_days: Maximum days to adjust

        Returns:
            Tuple of (validated_date, days_adjusted)
        """
        if not self.db_engine or vin not in self.vin_to_vehicle_id:
            return None, 0

        # Check if target date already has valid activity
        activity_data = self._query_vehicle_activity_on_date(vin, target_date)
        if activity_data and activity_data["has_km_activity"]:
            return target_date, 0

        # Try adjusting the date iteratively
        for days in range(1, max_days + 1):
            if direction == "forward":
                # For start dates: increase by 1 day iteratively
                adjusted_date = target_date + timedelta(days=days)
            else:
                # For end dates: decrease by 1 day iteratively
                adjusted_date = target_date - timedelta(days=days)

            activity_data = self._query_vehicle_activity_on_date(vin, adjusted_date)
            if activity_data and activity_data["has_km_activity"]:
                return adjusted_date, days

        return None, 0

    def _validate_battery_installation_with_activity(
        self, vin: str, installation_date: datetime
    ) -> bool:
        """Validate if battery installation date aligns with vehicle KM activity."""
        if not self.db_engine or vin not in self.vin_to_vehicle_id:
            return False

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            # Check KM activity around installation date (±7 days window)
            start_date = installation_date - timedelta(days=7)
            end_date = installation_date + timedelta(days=7)

            query = text(
                """
                SELECT COUNT(*) as active_days,
                       AVG(CASE WHEN km_end > km_start AND km_end - km_start >= 2 THEN km_end - km_start ELSE NULL END) as avg_km
                FROM public.daily_stats 
                WHERE vehicle_id = :vehicle_id 
                AND date BETWEEN :start_date AND :end_date
                AND km_end - km_start >= 2 
                AND km_end > km_start 
                AND km_start >= 0
            """
            )

            with self.db_engine.connect() as conn:
                result = conn.execute(
                    query,
                    {
                        "vehicle_id": vehicle_id,
                        "start_date": start_date.date(),
                        "end_date": end_date.date(),
                    },
                )
                row = result.fetchone()

                if row and row[0] > 0:  # At least some active days
                    active_days = row[0]
                    avg_km = row[1] or 0

                    # Validate meaningful KM activity
                    return active_days >= 2 and avg_km >= 2

        except Exception as e:
            logger.debug(
                f"Error validating installation for {vin} on {installation_date}: {e}"
            )

        return False

    def _validate_periods_with_activity(self, periods: List[Dict]) -> List[Dict]:
        """Validate periods against vehicle activity data."""
        for period in periods:
            vin = period["vin"]
            # Re-enable activity validation for comprehensive analysis
            activity = self._query_vehicle_activity_on_fly(vin, "summary")
            if activity:  # Re-enabled for comprehensive validation
                try:
                    validation_result = self._validate_period_activity(period)
                    period["activity_validated"] = validation_result["is_valid"]
                    period["km_validated"] = validation_result["km_valid"]

                    if validation_result["issues"]:
                        existing_note = period.get("note", "")
                        issues_note = (
                            f"Activity issues: {', '.join(validation_result['issues'])}"
                        )
                        period["note"] = f"{existing_note} | {issues_note}".strip(" |")

                except Exception as e:
                    logger.debug(f"Error validating period activity: {e}")

        return periods

    def _validate_period_activity(self, period: Dict) -> Dict:
        """Validate a single period against KM activity data only."""
        vin = period["vin"]
        start_date = period["start_date"]
        end_date = period["end_date"]

        if not start_date or vin not in self.vin_to_vehicle_id:
            return {
                "is_valid": False,
                "km_valid": False,
                "issues": ["no_validation_data"],
            }

        vehicle_id = self.vin_to_vehicle_id[vin]

        try:
            # Query KM activity during the period only
            query = text(
                """
                SELECT 
                    COUNT(*) as total_days,
                    COUNT(CASE WHEN km_end > km_start AND km_end - km_start >= 2 AND km_end >= 0 AND km_start >= 0 THEN 1 END) as meaningful_km_days,
                    AVG(CASE WHEN km_end > km_start AND km_end - km_start >= 2 THEN km_end - km_start ELSE NULL END) as avg_meaningful_km,
                    COUNT(CASE WHEN km_end <= km_start OR km_end < 0 OR km_start < 0 THEN 1 END) as invalid_km_days
                FROM public.daily_stats 
                WHERE vehicle_id = :vehicle_id 
                AND date BETWEEN :start_date AND :end_date
            """
            )

            end_date_query = end_date.date() if end_date else datetime.now().date()

            with self.db_engine.connect() as conn:
                result = conn.execute(
                    query,
                    {
                        "vehicle_id": vehicle_id,
                        "start_date": start_date.date(),
                        "end_date": end_date_query,
                    },
                )
                row = result.fetchone()

                if not row or row[0] == 0:
                    return {
                        "is_valid": False,
                        "km_valid": False,
                        "issues": ["no_activity_data"],
                    }

                (total_days, meaningful_km_days, avg_meaningful_km, invalid_km_days) = (
                    row
                )

                issues = []
                km_valid = True

                # Enhanced validation with meaningful KM activity thresholds
                if total_days > 7:  # Only check for periods longer than a week
                    meaningful_km_ratio = (
                        meaningful_km_days / total_days if total_days > 0 else 0
                    )

                    if (
                        meaningful_km_ratio < 0.1
                    ):  # Less than 10% meaningful KM activity
                        issues.append("low_meaningful_km_activity")
                        km_valid = False

                # Validate KM patterns with enhanced thresholds
                if meaningful_km_days > 0:
                    if (
                        avg_meaningful_km
                        and avg_meaningful_km > self.avg_km_per_day * 3
                    ):  # 60km/day
                        issues.append("unusually_high_km")
                    elif (
                        avg_meaningful_km and avg_meaningful_km < 2
                    ):  # Below meaningful threshold
                        issues.append("unusually_low_meaningful_km")
                else:
                    if total_days > 30:  # Only flag if period is longer than a month
                        issues.append("no_meaningful_km_activity")
                        km_valid = False

                # Check KM data quality
                if total_days > 0:
                    invalid_km_ratio = invalid_km_days / total_days

                    if invalid_km_ratio > 0.5:  # More than 50% invalid km data
                        issues.append("high_invalid_km_data")
                        km_valid = False

                is_valid = len(issues) == 0

                return {
                    "is_valid": is_valid,
                    "km_valid": km_valid,
                    "issues": issues,
                    "total_days": total_days,
                    "meaningful_km_days": meaningful_km_days,
                    "avg_meaningful_km": avg_meaningful_km,
                    "invalid_km_days": invalid_km_days,
                }

        except Exception as e:
            logger.debug(f"Error in enhanced period validation: {e}")
            return {
                "is_valid": False,
                "km_valid": False,
                "issues": ["validation_error"],
            }

    def _validate_battery_start_with_activity(
        self, vin: str, start_date: datetime
    ) -> bool:
        """Validate if battery start date aligns with vehicle activity."""
        activity = self._query_vehicle_activity_on_fly(vin, "summary")
        if not activity:
            return False

        # Check if start date is within reasonable range of first activity
        if activity["first_activity_date"]:
            days_diff = abs((start_date.date() - activity["first_activity_date"]).days)
            return days_diff <= 30  # Within 30 days of first activity

        return False

    def _add_snapshot_only_batteries(self, processed_batteries: Set[str]) -> List[Dict]:
        """Add batteries that only appear in snapshots."""
        snapshot_periods = []

        for vin, vehicle_info in self.vehicle_info_cache.items():
            snapshot_batteries = [
                vehicle_info.get("master_battery"),
                vehicle_info.get("slave_battery"),
            ]

            for battery_id in snapshot_batteries:
                if (
                    battery_id
                    and pd.notna(battery_id)
                    and battery_id not in processed_batteries
                    and (
                        not self.test_batteries or battery_id in self.test_batteries
                    )  # Filter for test batteries
                ):

                    erstzulassung = vehicle_info.get("erstzulassung")
                    if erstzulassung:
                        period = self._create_period(
                            battery_id,
                            vin,
                            erstzulassung,
                            None,
                            "snapshot_only",
                            "medium",
                            "active",
                            note="Snapshot-only battery - no repair events found",
                        )

                        # Re-enable activity validation for comprehensive analysis
                        if self._validate_battery_start_with_activity(
                            vin, erstzulassung
                        ):
                            period["confidence"] = "high"
                            period["activity_validated"] = True

                        snapshot_periods.append(period)
                        processed_batteries.add(battery_id)

        return snapshot_periods

    # =================================================================
    # PHASE 3: COMPREHENSIVE VALIDATION AND QUALITY ASSURANCE
    # =================================================================

    def phase3_comprehensive_validation(self) -> Dict:
        """Phase 3: Final validation, deduplication, and quality assurance."""
        logger.info("=== PHASE 3: COMPREHENSIVE VALIDATION AND QUALITY ASSURANCE ===")

        # Start with timeline from Phase 2
        self.final_timeline = self.battery_timelines.copy()

        # Apply comprehensive validation
        dedup_stats = self._perform_deduplication()
        conflict_stats = self._resolve_timeline_conflicts()
        activity_stats = self._enhance_with_activity_validation()
        lifecycle_stats = self._finalize_lifecycle_stages()
        quality_metrics = self._calculate_final_quality_metrics()

        self.quality_stats = {
            "duplicates_removed": dedup_stats,
            "conflicts_resolved": conflict_stats,
            "activity_validated_periods": activity_stats,
            "lifecycle_stages_assigned": lifecycle_stats,
            "final_timeline_size": len(self.final_timeline),
            "overall_quality_score": quality_metrics["overall_score"],
            "quality_metrics": quality_metrics,
        }

        logger.info(f"Phase 3 complete: {self.quality_stats}")
        return self.quality_stats

    def _perform_deduplication(self) -> int:
        """Remove exact duplicates and merge compatible periods."""
        logger.info("Performing deduplication...")

        original_count = len(self.final_timeline)

        # Convert to DataFrame for easier deduplication
        df = pd.DataFrame(self.final_timeline)

        # Remove exact duplicates
        df = df.drop_duplicates(
            subset=["battery_id", "vin", "start_date", "end_date"], keep="first"
        )

        # Convert back to list
        self.final_timeline = df.to_dict("records")

        duplicates_removed = original_count - len(self.final_timeline)
        logger.info(f"Removed {duplicates_removed} duplicates")
        return duplicates_removed

    def _resolve_timeline_conflicts(self) -> int:
        """Resolve overlapping periods and conflicts."""
        logger.info("Resolving timeline conflicts...")

        conflicts_resolved = 0

        # Group periods by battery_id
        battery_groups = {}
        for period in self.final_timeline:
            battery_id = period["battery_id"]
            if battery_id not in battery_groups:
                battery_groups[battery_id] = []
            battery_groups[battery_id].append(period)

        # Resolve conflicts within each battery
        clean_periods = []
        for battery_id, periods in battery_groups.items():
            if len(periods) <= 1:
                clean_periods.extend(periods)
                continue

            # Sort by start date
            periods.sort(key=lambda x: x["start_date"] or datetime.min)

            # Check for overlaps and resolve
            i = 0
            while i < len(periods):
                current = periods[i]
                overlapping = [current]

                # Find overlapping periods
                j = i + 1
                while j < len(periods):
                    if self._periods_overlap(current, periods[j]):
                        overlapping.append(periods[j])
                        periods.pop(j)
                    else:
                        j += 1

                if len(overlapping) > 1:
                    # Resolve by keeping highest confidence period
                    best_period = max(
                        overlapping,
                        key=lambda x: self._confidence_score(x["confidence"]),
                    )
                    clean_periods.append(best_period)
                    conflicts_resolved += len(overlapping) - 1
                else:
                    clean_periods.extend(overlapping)

                i += 1

        self.final_timeline = clean_periods
        logger.info(f"Resolved {conflicts_resolved} conflicts")
        return conflicts_resolved

    def _enhance_with_activity_validation(self) -> int:
        """Enhance periods with comprehensive activity validation."""
        logger.info("Enhancing with activity validation...")

        validated_periods = 0

        for period in self.final_timeline:
            if not period.get("activity_validated", False):
                # Apply validation if not already done
                validated_periods += 1
                # Validation logic already applied in Phase 2, just counting here

        logger.info(f"Validated {validated_periods} periods with activity data")
        return validated_periods

    def _finalize_lifecycle_stages(self) -> int:
        """Finalize lifecycle stages with activity-based validation."""
        logger.info("Finalizing lifecycle stages...")

        stages_assigned = 0

        # Group periods by battery for lifecycle analysis
        battery_groups = {}
        for period in self.final_timeline:
            battery_id = period["battery_id"]
            if battery_id not in battery_groups:
                battery_groups[battery_id] = []
            battery_groups[battery_id].append(period)

        for battery_id, periods in battery_groups.items():
            # Sort periods chronologically
            periods.sort(key=lambda x: x["start_date"] or datetime.min)

            for i, period in enumerate(periods):
                # Enhanced lifecycle stage determination
                if i == 0:
                    # First period - check if it's near vehicle rollout
                    vehicle_info = self.vehicle_info_cache.get(period["vin"], {})
                    erstzulassung = vehicle_info.get("erstzulassung")

                    if (
                        period["start_date"]
                        and erstzulassung
                        and abs((period["start_date"] - erstzulassung).days) <= 30
                    ):
                        period["lifecycle_stage"] = "new"
                    else:
                        period["lifecycle_stage"] = "transferred"

                elif period["end_date"] is None:
                    # Currently active
                    period["lifecycle_stage"] = "active"

                    # Validate with current activity
                    if period.get("activity_validated"):
                        period["lifecycle_stage"] = "active_validated"

                elif i < len(periods) - 1:
                    # Intermediate period
                    next_period = periods[i + 1]
                    if period["vin"] != next_period["vin"]:
                        period["lifecycle_stage"] = "transferred"
                    else:
                        period["lifecycle_stage"] = "replaced"

                else:
                    # Last period for this battery
                    period["lifecycle_stage"] = "removed"

                stages_assigned += 1

        logger.info(f"Assigned lifecycle stages to {stages_assigned} periods")
        return stages_assigned

    def _calculate_final_quality_metrics(self) -> Dict:
        """Calculate comprehensive quality metrics."""
        if not self.final_timeline:
            return {"overall_score": 0.0}

        total_periods = len(self.final_timeline)

        # Confidence distribution
        confidence_dist = {"high": 0, "medium": 0, "low": 0}
        for period in self.final_timeline:
            confidence_dist[period["confidence"]] += 1

        # Source distribution
        source_dist = {"repair_event": 0, "snapshot_only": 0}
        for period in self.final_timeline:
            source = period["source"]
            if source in source_dist:
                source_dist[source] += 1

        # Validation status
        activity_validated = len(
            [p for p in self.final_timeline if p.get("activity_validated")]
        )
        km_validated = len([p for p in self.final_timeline if p.get("km_validated")])

        # Completeness
        complete_periods = len(
            [p for p in self.final_timeline if p["start_date"] and p["end_date"]]
        )
        active_periods = len(
            [p for p in self.final_timeline if p["start_date"] and not p["end_date"]]
        )

        # Calculate overall quality score
        confidence_score = (
            confidence_dist["high"] * 1.0
            + confidence_dist["medium"] * 0.7
            + confidence_dist["low"] * 0.3
        ) / total_periods

        completeness_score = (complete_periods + active_periods * 0.8) / total_periods
        validation_score = (activity_validated + km_validated) / (total_periods * 2)

        overall_score = (
            confidence_score * 0.4 + completeness_score * 0.3 + validation_score * 0.3
        )

        return {
            "overall_score": round(overall_score, 3),
            "confidence_distribution": confidence_dist,
            "source_distribution": source_dist,
            "validation_status": {
                "activity_validated": activity_validated,
                "km_validated": km_validated,
            },
            "completeness": {
                "complete": complete_periods,
                "active": active_periods,
                "incomplete": total_periods - complete_periods - active_periods,
            },
        }

    # Helper methods
    def _periods_overlap(self, period1: Dict, period2: Dict) -> bool:
        """Check if two periods overlap in time."""
        start1 = period1["start_date"] or datetime.min
        end1 = period1["end_date"] or datetime.max
        start2 = period2["start_date"] or datetime.min
        end2 = period2["end_date"] or datetime.max

        return start1 < end2 and start2 < end1

    def _confidence_score(self, confidence: str) -> int:
        """Convert confidence level to numeric score."""
        scores = {"high": 3, "medium": 2, "low": 1}
        return scores.get(confidence, 0)

    # =================================================================
    # MAIN PIPELINE ORCHESTRATION
    # =================================================================

    def run_streamlined_analysis(self) -> Dict:
        """Run the complete 3-phase battery timeline analysis."""
        logger.info("Starting 3-phase streamlined battery timeline analysis...")

        start_time = datetime.now()

        try:
            # Phase 1: Data Loading and Cleaning
            phase1_stats = self.phase1_load_and_clean_data()

            # Phase 2: Unified Timeline Building
            phase2_stats = self.phase2_unified_timeline_building()

            # Phase 3: Comprehensive Validation
            phase3_stats = self.phase3_comprehensive_validation()

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            # Store phase stats for reporting
            self.phase_stats = {
                "phase1": phase1_stats,
                "phase2": phase2_stats,
                "phase3": phase3_stats,
            }

            # Compile final results
            results = {
                "success": True,
                "processing_time_seconds": processing_time,
                "final_timeline": self.final_timeline,
                "phase_stats": self.phase_stats,
                "summary": {
                    "total_periods": len(self.final_timeline),
                    "unique_batteries": len(
                        set(p["battery_id"] for p in self.final_timeline)
                    ),
                    "unique_vehicles": len(set(p["vin"] for p in self.final_timeline)),
                    "overall_quality_score": self.quality_stats[
                        "overall_quality_score"
                    ],
                    "activity_validated_periods": self.quality_stats[
                        "activity_validated_periods"
                    ],
                },
            }

            logger.info("3-phase streamlined analysis completed successfully!")
            logger.info(f"Processing time: {processing_time:.2f} seconds")
            logger.info(f"Final timeline: {len(self.final_timeline)} periods")
            logger.info(f"Quality score: {self.quality_stats['overall_quality_score']}")
            logger.info(
                f"Activity validated: {self.quality_stats['activity_validated_periods']} periods"
            )

            return results

        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            return {"success": False, "error": str(e)}

    # =================================================================
    # EXPORT AND REPORTING METHODS
    # =================================================================

    def export_timeline(self, output_file: str = "battery_timeline.csv") -> str:
        """Export final timeline to CSV."""
        if not self.final_timeline:
            logger.warning("No timeline to export. Run analysis first.")
            return ""

        df = pd.DataFrame(self.final_timeline)
        df.to_csv(output_file, index=False)
        logger.info(f"Exported timeline to: {output_file}")
        return output_file

    def generate_summary_report(self) -> str:
        """Generate comprehensive summary report for 3-phase pipeline."""
        if not self.final_timeline:
            return "No timeline data available. Run analysis first."

        total_periods = len(self.final_timeline)
        unique_batteries = len(set(p["battery_id"] for p in self.final_timeline))
        unique_vehicles = len(set(p["vin"] for p in self.final_timeline))

        phase_stats = getattr(self, "phase_stats", {})
        quality_stats = getattr(self, "quality_stats", {})

        def format_number(value, fallback="N/A"):
            if isinstance(value, (int, float)) and value != "N/A":
                return f"{value:,}"
            return fallback

        # Calculate additional metrics
        activity_validated = (
            quality_stats.get("quality_metrics", {})
            .get("validation_status", {})
            .get("activity_validated", 0)
        )
        km_validated = (
            quality_stats.get("quality_metrics", {})
            .get("validation_status", {})
            .get("km_validated", 0)
        )

        report = f"""
STREAMLINED BATTERY TIMELINE ANALYSIS REPORT (3-PHASE)
{'='*70}

OVERVIEW:
- Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- Total Timeline Periods: {total_periods:,}
- Unique Batteries: {unique_batteries:,}
- Unique Vehicles: {unique_vehicles:,}
- Overall Quality Score: {quality_stats.get('overall_quality_score', 'N/A')}
- Activity Validated Periods: {format_number(activity_validated)}
- Km Validated Periods: {format_number(km_validated)}

STREAMLINED PROCESSING RESULTS:

Phase 1 - Data Loading & Cleaning:
- Raw Repair Records: {format_number(phase_stats.get('phase1', {}).get('raw_repair_records'))}
- Clean Events: {format_number(phase_stats.get('phase1', {}).get('clean_events'))}
- VINs with Activity Mapping: {format_number(phase_stats.get('phase1', {}).get('vehicles_with_activity'))}
- Global Battery Cache: {format_number(phase_stats.get('phase1', {}).get('global_batteries'))}
- Data Quality Score: {phase_stats.get('phase1', {}).get('data_quality_score', 'N/A')}

Phase 2 - Unified Timeline Building:
- VINs Processed: {format_number(phase_stats.get('phase2', {}).get('vins_processed'))}
- Batteries Processed: {format_number(phase_stats.get('phase2', {}).get('batteries_processed'))}
- Periods Created: {format_number(phase_stats.get('phase2', {}).get('periods_created'))}
- Edge Cases Handled: {format_number(phase_stats.get('phase2', {}).get('edge_cases_handled'))}

Phase 3 - Comprehensive Validation:
- Duplicates Removed: {format_number(phase_stats.get('phase3', {}).get('duplicates_removed'))}
- Conflicts Resolved: {format_number(phase_stats.get('phase3', {}).get('conflicts_resolved'))}
- Lifecycle Stages Assigned: {format_number(phase_stats.get('phase3', {}).get('lifecycle_stages_assigned'))}
- Final Quality Score: {quality_stats.get('overall_quality_score', 'N/A')}

CONFIDENCE DISTRIBUTION:
- High Confidence: {format_number(quality_stats.get('quality_metrics', {}).get('confidence_distribution', {}).get('high'))} periods
- Medium Confidence: {format_number(quality_stats.get('quality_metrics', {}).get('confidence_distribution', {}).get('medium'))} periods  
- Low Confidence: {format_number(quality_stats.get('quality_metrics', {}).get('confidence_distribution', {}).get('low'))} periods

VALIDATION STATUS:
- Activity Validated: {format_number(activity_validated)} periods
- Km Validated: {format_number(km_validated)} periods
- PostgreSQL Integration: {'✅ Active' if self.db_engine else '❌ Disabled'}

STREAMLINED APPROACH BENEFITS:
✓ Battery-centric processing with descending date sorting
✓ Inline edge case handling reduces processing loops  
✓ PostgreSQL integration for vehicle activity validation
✓ On-demand activity data loading with caching (memory efficient)
✓ Comprehensive validation rules for km data only
✓ Single-pass timeline building with immediate confidence scoring
✓ Enhanced gap inference using fleet patterns

Fleet Validation Parameters:
- Average km/day: {self.avg_km_per_day} km
- Average SOC usage/day: {self.avg_soc_usage_per_day}%
- Validation Rules: km_end > km_start ≥ 0, 0 ≤ SOC ≤ 100

The streamlined 3-phase approach provides efficient processing while maintaining
comprehensive validation and quality assurance through activity data integration.
"""

        return report

    def export_results(
        self, output_dir: str = "battery_timeline_results"
    ) -> Dict[str, str]:
        """Export all results including enhanced statistics."""
        import os

        os.makedirs(output_dir, exist_ok=True)
        export_files = {}

        # Export final timeline
        timeline_file = os.path.join(output_dir, "battery_timeline.csv")
        self.export_timeline(timeline_file)
        export_files["timeline"] = timeline_file

        # Export summary report
        report = self.generate_summary_report()
        report_file = os.path.join(output_dir, "battery_timeline_report.txt")
        with open(report_file, "w") as f:
            f.write(report)
        export_files["report"] = report_file

        # Export phase statistics
        if hasattr(self, "phase_stats"):
            phase_stats_file = os.path.join(output_dir, "phase_statistics.csv")
            phase_data = []

            for phase_name, stats in self.phase_stats.items():
                for metric, value in stats.items():
                    phase_data.append(
                        {"phase": phase_name, "metric": metric, "value": value}
                    )

            if phase_data:
                pd.DataFrame(phase_data).to_csv(phase_stats_file, index=False)
                export_files["phase_stats"] = phase_stats_file

        # Export validation statistics (only for cached activity data)
        if self.vehicle_activity_cache:
            validation_file = os.path.join(output_dir, "activity_validation.csv")
            validation_data = []

            for vin, activity in self.vehicle_activity_cache.items():
                if activity:  # Only include non-None cached entries
                    validation_data.append(
                        {
                            "vin": vin,
                            "vehicle_id": activity["vehicle_id"],
                            "total_days": activity["total_days"],
                            "days_with_km": activity["days_with_km"],
                            "avg_daily_km": activity["avg_daily_km"],
                            "data_quality_score": activity["data_quality_score"],
                            "invalid_km_records": activity["invalid_km_records"],
                        }
                    )

            if validation_data:
                pd.DataFrame(validation_data).to_csv(validation_file, index=False)
                export_files["validation"] = validation_file

        logger.info(f"Exported results to: {output_dir}")
        return export_files

    # Legacy compatibility
    def run_analysis(self) -> Dict:
        """Legacy compatibility method - runs streamlined analysis."""
        return self.run_streamlined_analysis()


def main():
    """Main function to run streamlined battery timeline analysis."""

    hv_repair_file = "hv_repair_2025-06-02b.csv"
    working_matching_vehicles_file = "comparison_results/working_matching_vehicles.csv"
    working_unique_vehicles_file = "comparison_results/working_unique_vehicles.csv"

    # Test batteries for quick testing (set to None for full processing)
    # test_batteries = None  # Enable full processing with activity validation
    test_batteries = None
    test_batteries = [
        "V6P0116B000AA01438",
        "V6P0116B000AA00557",
        "22438",
        "10459",
        "10001",
        "22912",
        "27514",
        "21194",
        "29307",
        "26769",
        "28108",
        "27349",
    ]

    pipeline = BatteryTimelinePipeline(
        hv_repair_file,
        working_matching_vehicles_file,
        working_unique_vehicles_file,
        test_batteries=test_batteries,  # Enable test mode
    )

    try:
        print("STREAMLINED BATTERY TIMELINE ANALYSIS (3-PHASE)")
        print("=" * 60)
        if test_batteries:
            print(
                f"🧪 TEST MODE: Processing only {len(test_batteries)} specific batteries"
            )
            print(f"Test batteries: {', '.join(test_batteries)}")
            print()
        else:
            print(
                "🚀 FULL PROCESSING MODE: Processing all batteries with comprehensive validation"
            )
            print()
        print("Streamlined 3-Phase Approach:")
        print("  Phase 1: Data Loading and Cleaning (with PostgreSQL integration)")
        print("  Phase 2: Unified Timeline Building (Battery-Centric)")
        print("  Phase 3: Comprehensive Validation and Quality Assurance")
        print()
        print("Key Enhancements:")
        print("  ✓ PostgreSQL integration for vehicle activity validation")
        print("  ✓ Comprehensive activity-based validation (RE-ENABLED)")
        print("  ✓ Battery-centric processing with descending date sorting")
        print("  ✓ Inline edge case handling")
        print("  ✓ Enhanced gap inference using fleet patterns")
        print()

        # Run streamlined analysis
        results = pipeline.run_streamlined_analysis()

        if results["success"]:
            # Export results
            output_dir = (
                "test_battery_results" if test_batteries else "battery_timeline_results"
            )
            export_files = pipeline.export_results(output_dir)

            # Display summary
            print(pipeline.generate_summary_report())
            print(f"\nResults exported to: {export_files}")
        else:
            print(f"Analysis failed: {results.get('error', 'Unknown error')}")

    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        raise


if __name__ == "__main__":
    main()
